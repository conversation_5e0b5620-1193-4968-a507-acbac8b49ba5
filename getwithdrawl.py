import playwright
from playwright.sync_api import sync_playwright

with sync_playwright() as p:
    browser = p.chromium.launch(headless=False)
    user_data_dir = r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data"
    profile_name = "Profile 1"
    browser = p.chromium.launch(headless=False, args=[f"--user-data-dir={user_data_dir}", f"--profile-directory={profile_name}"])
    page = browser.new_page()
    page.goto("https://www.youtube.com")
    page.wait_for_load_state("networkidle")
    browser.close()
